import { CustomError } from "@/utils/custom-error";

export function validateProductCreationData(formData: FormData): void {
  const requiredLanguages = ["Arabic", "French", "English"];
  const missingFields: string[] = [];

  const contentField = formData.get("content");
  if (contentField && typeof contentField === "string") {
    try {
      const content = JSON.parse(contentField);
      if (Array.isArray(content)) {
        requiredLanguages.forEach((lang) => {
          const langContent = content.find((item) => item.language === lang);
          if (
            !langContent ||
            !langContent.name ||
            langContent.name.trim() === ""
          ) {
            missingFields.push(`${lang} name`);
          }
        });
      } else {
        throw new CustomError(`Invalid Data!`, 400);
      }
    } catch (error) {
      throw new CustomError(`Invalid Data!`, 400);
    }
  } else {
    throw new CustomError(`Missed Data!`, 400);
  }

  // Validate FAQs structure
  const faqsField = formData.get("faqs");
  if (faqsField && typeof faqsField === "string") {
    try {
      const faqs = JSON.parse(faqsField);
      if (Array.isArray(faqs)) {
        faqs.forEach((faq, index) => {
          if (!faq.content || !Array.isArray(faq.content)) {
            missingFields.push(`FAQ ${index + 1} content structure`);
            return;
          }

          faq.content.forEach((langContent: any) => {
            if (!langContent.question || langContent.question.trim() === "") {
              missingFields.push(
                `FAQ ${index + 1} ${langContent.language} question`
              );
            }
            if (!langContent.answer || langContent.answer.trim() === "") {
              missingFields.push(
                `FAQ ${index + 1} ${langContent.language} answer`
              );
            }
          });
        });
      }
    } catch (error) {
      // FAQs are optional, so we don't throw an error here
    }
  }

  // Validate Sections structure
  const sectionsField = formData.get("sections");
  if (sectionsField && typeof sectionsField === "string") {
    try {
      const sections = JSON.parse(sectionsField);
      if (Array.isArray(sections)) {
        sections.forEach((section, index) => {
          if (!section.content || !Array.isArray(section.content)) {
            missingFields.push(`Section ${index + 1} content structure`);
            return;
          }

          section.content.forEach((langContent: any) => {
            if (!langContent.title || langContent.title.trim() === "") {
              missingFields.push(
                `Section ${index + 1} ${langContent.language} title`
              );
            }
            if (
              !langContent.description ||
              langContent.description.trim() === ""
            ) {
              missingFields.push(
                `Section ${index + 1} ${langContent.language} description`
              );
            }
          });
        });
      }
    } catch (error) {
      // Sections are optional, so we don't throw an error here
    }
  }

  const categoryIds = formData.getAll("categoryIds");
  if (!categoryIds || categoryIds.length === 0 || categoryIds[0] === "") {
    missingFields.push("categoryIds");
  }

  if (missingFields.length > 0) {
    throw new CustomError(`Missed Data!`, 400);
  }

  const displayOrder = formData.get("displayOrder");
  if (displayOrder !== null && typeof displayOrder === "string") {
    if (parseFloat(displayOrder) < 1) {
      throw new CustomError(`Invalid Data!`, 400);
    }
  }
}
