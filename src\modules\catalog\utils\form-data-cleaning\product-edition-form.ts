import { buildMultilanguageContent } from "./build-multilanguage-content";

export function cleanProductEditionFormData(
  formData: FormData,
  currentLanguage: string
): FormData {
  const cleanedData = new FormData();
  const categoryIds: string[] = [];
  const sections: any[] = [];
  const faqs: any[] = [];
  const sectionContentMap: { [key: number]: { [key: string]: any } } = {};
  const faqContentMap: { [key: number]: { [key: string]: any } } = {};

  const excludedFields = [
    "keywords",
    "seoContent",
    "metaTitle_arabic",
    "metaDescription_arabic",
    "metaTitle_french",
    "metaDescription_french",
    "metaTitle_english",
    "metaDescription_english",
  ];

  const content = buildMultilanguageContent(formData, currentLanguage, true);

  for (const [key, value] of formData.entries()) {
    const stringValue = value as string;

    if (excludedFields.includes(key)) {
      continue;
    }

    if (key === "categoryIds") {
      categoryIds.push(stringValue);
    } else if (key.startsWith("sections[")) {
      const sectionMatch = key.match(/sections\[(\d+)\]\.(.+)_(.+)/);
      const displayOrderMatch = key.match(/sections\[(\d+)\]\.displayOrder$/);

      if (sectionMatch) {
        const index = parseInt(sectionMatch[1]);
        const field = sectionMatch[2];
        const language = sectionMatch[3];

        if (!sectionContentMap[index]) {
          sectionContentMap[index] = {};
        }
        if (!sectionContentMap[index][language]) {
          sectionContentMap[index][language] = {
            language: language.charAt(0).toUpperCase() + language.slice(1),
          };
        }
        sectionContentMap[index][language][field] = stringValue;
      } else if (displayOrderMatch) {
        const index = parseInt(displayOrderMatch[1]);
        if (!sections[index]) {
          sections[index] = { displayOrder: index + 1, content: [] };
        }
        sections[index].displayOrder = parseInt(stringValue) || index + 1;
      }
    } else if (key.startsWith("faqs[")) {
      const faqMatch = key.match(/faqs\[(\d+)\]\.(.+)_(.+)/);
      const displayOrderMatch = key.match(/faqs\[(\d+)\]\.displayOrder$/);

      if (faqMatch) {
        const index = parseInt(faqMatch[1]);
        const field = faqMatch[2];
        const language = faqMatch[3];

        if (!faqContentMap[index]) {
          faqContentMap[index] = {};
        }
        if (!faqContentMap[index][language]) {
          faqContentMap[index][language] = {
            language: language.charAt(0).toUpperCase() + language.slice(1),
          };
        }
        faqContentMap[index][language][field] = stringValue;
      } else if (displayOrderMatch) {
        const index = parseInt(displayOrderMatch[1]);
        if (!faqs[index]) {
          faqs[index] = { displayOrder: index + 1, content: [] };
        }
        faqs[index].displayOrder = parseInt(stringValue) || index + 1;
      }
    } else if (
      !key.includes("_arabic") &&
      !key.includes("_french") &&
      !key.includes("_english")
    ) {
      if (key === "brandId" && value === "") {
        cleanedData.append(key, "null");
      } else {
        cleanedData.append(key, value);
      }
    }
  }

  categoryIds.forEach((id) => cleanedData.append("categoryIds", id));

  if (content.length > 0) {
    cleanedData.append("content", JSON.stringify(content));
  }

  // Process sections content
  Object.keys(sectionContentMap).forEach((indexStr) => {
    const index = parseInt(indexStr);
    if (!sections[index]) {
      sections[index] = { displayOrder: index + 1, content: [] };
    }

    Object.keys(sectionContentMap[index]).forEach((lang) => {
      const langContent = sectionContentMap[index][lang];
      if (langContent.title && langContent.description) {
        sections[index].content.push(langContent);
      }
    });
  });

  // Process faqs content
  Object.keys(faqContentMap).forEach((indexStr) => {
    const index = parseInt(indexStr);
    if (!faqs[index]) {
      faqs[index] = { displayOrder: index + 1, content: [] };
    }

    Object.keys(faqContentMap[index]).forEach((lang) => {
      const langContent = faqContentMap[index][lang];
      if (langContent.question && langContent.answer) {
        faqs[index].content.push(langContent);
      }
    });
  });

  // Add sections if any exist
  if (sections.length > 0) {
    const validSections = sections.filter(
      (section) => section && section.content && section.content.length > 0
    );
    if (validSections.length > 0) {
      cleanedData.append("sections", JSON.stringify(validSections));
    }
  }

  // Add faqs if any exist
  if (faqs.length > 0) {
    const validFaqs = faqs.filter(
      (faq) => faq && faq.content && faq.content.length > 0
    );
    if (validFaqs.length > 0) {
      cleanedData.append("faqs", JSON.stringify(validFaqs));
    }
  }

  return cleanedData;
}
