import { buildMultilanguageContent } from "./build-multilanguage-content";

export function cleanProductEditionFormData(
  formData: FormData,
  currentLanguage: string
): FormData {
  const cleanedData = new FormData();
  const categoryIds: string[] = [];

  const excludedFields = [
    "keywords",
    "seoContent",
    "metaTitle_arabic",
    "metaDescription_arabic",
    "metaTitle_french",
    "metaDescription_french",
    "metaTitle_english",
    "metaDescription_english",
  ];

  const sectionContentMap: Record<number, Record<string, any>> = {};
  const sections: Array<{ displayOrder: number; content: any[] }> = [];

  const faqContentMap: Record<number, Record<string, any>> = {};
  const faqs: Array<{ displayOrder: number; content: any[] }> = [];

  const content = buildMultilanguageContent(formData, currentLanguage, true);

  for (const [key, value] of formData.entries()) {
    const stringValue = value as string;

    if (excludedFields.includes(key)) continue;

    if (key === "categoryIds") {
      categoryIds.push(stringValue);
    } else if (key.startsWith("sections[")) {
      const sectionMatch = key.match(/sections\[(\d+)\]\.(.+)_(.+)/);
      const displayOrderMatch = key.match(/sections\[(\d+)\]\.displayOrder$/);

      if (sectionMatch) {
        const index = parseInt(sectionMatch[1]);
        const field = sectionMatch[2];
        const language = sectionMatch[3];

        sectionContentMap[index] ||= {};
        sectionContentMap[index][language] ||= {
          language: language.charAt(0).toUpperCase() + language.slice(1),
        };
        sectionContentMap[index][language][field] = stringValue;
      } else if (displayOrderMatch) {
        const index = parseInt(displayOrderMatch[1]);
        sections[index] ||= { displayOrder: index + 1, content: [] };
        sections[index].displayOrder = parseInt(stringValue) || index + 1;
      }
    } else if (key.startsWith("faqs[")) {
      const faqMatch = key.match(/faqs\[(\d+)\]\.(.+)_(.+)/);
      const displayOrderMatch = key.match(/faqs\[(\d+)\]\.displayOrder$/);

      if (faqMatch) {
        const index = parseInt(faqMatch[1]);
        const field = faqMatch[2];
        const language = faqMatch[3];

        faqContentMap[index] ||= {};
        faqContentMap[index][language] ||= {
          language: language.charAt(0).toUpperCase() + language.slice(1),
        };
        faqContentMap[index][language][field] = stringValue;
      } else if (displayOrderMatch) {
        const index = parseInt(displayOrderMatch[1]);
        faqs[index] ||= { displayOrder: index + 1, content: [] };
        faqs[index].displayOrder = parseInt(stringValue) || index + 1;
      }
    } else if (
      !key.includes("_arabic") &&
      !key.includes("_french") &&
      !key.includes("_english")
    ) {
      if (key === "brandId" && stringValue === "") {
        cleanedData.append(key, "null");
      } else {
        cleanedData.append(key, value);
      }
    }
  }

  categoryIds.forEach((id) => cleanedData.append("categoryIds", id));

  if (content.length > 0) {
    cleanedData.append("content", JSON.stringify(content));
  }

  // Finalize sections
  Object.keys(sectionContentMap).forEach((indexStr) => {
    const index = parseInt(indexStr);
    sections[index] ||= { displayOrder: index + 1, content: [] };

    ["arabic", "french", "english"].forEach((lang) => {
      const languageKey = lang.charAt(0).toUpperCase() + lang.slice(1);
      const langContent = sectionContentMap[index]?.[lang] || {
        title: "",
        description: "",
        language: languageKey,
      };

      sections[index].content.push({
        title: langContent.title || "",
        description: langContent.description || "",
        language: languageKey,
      });
    });
  });

  // Finalize FAQs
  Object.keys(faqContentMap).forEach((indexStr) => {
    const index = parseInt(indexStr);
    faqs[index] ||= { displayOrder: index + 1, content: [] };

    ["arabic", "french", "english"].forEach((lang) => {
      const languageKey = lang.charAt(0).toUpperCase() + lang.slice(1);
      const langContent = faqContentMap[index]?.[lang] || {
        question: "",
        answer: "",
        language: languageKey,
      };

      faqs[index].content.push({
        question: langContent.question || "",
        answer: langContent.answer || "",
        language: languageKey,
      });
    });
  });

  if (sections.length > 0) {
    cleanedData.append("sections", JSON.stringify(sections));
  }

  if (faqs.length > 0) {
    cleanedData.append("faqs", JSON.stringify(faqs));
  }

  return cleanedData;
}
