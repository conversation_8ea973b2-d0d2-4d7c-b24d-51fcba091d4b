import { buildMultilanguageContent } from "./build-multilanguage-content";

export function cleanProductEditionFormData(
  formData: FormData,
  currentLanguage: string
): FormData {
  const cleanedData = new FormData();
  const categoryIds: string[] = [];
  const sections: any[] = [];
  const faqs: any[] = [];

  const excludedFields = [
    "keywords",
    "seoContent",
    "metaTitle_arabic",
    "metaDescription_arabic",
    "metaTitle_french",
    "metaDescription_french",
    "metaTitle_english",
    "metaDescription_english",
  ];

  const content = buildMultilanguageContent(formData, currentLanguage, true);

  for (const [key, value] of formData.entries()) {
    const stringValue = value as string;

    if (excludedFields.includes(key)) {
      continue;
    }

    if (key === "categoryIds") {
      categoryIds.push(stringValue);
    } else if (key.startsWith("sections[")) {
      const match = key.match(/sections\[(\d+)\]\.(.+)/);
      if (match) {
        const index = parseInt(match[1]);
        const field = match[2];
        if (!sections[index]) {
          sections[index] = { displayOrder: index + 1 };
        }
        sections[index][field] =
          field === "displayOrder"
            ? parseInt(stringValue) || index + 1
            : stringValue;
      }
    } else if (key.startsWith("faqs[")) {
      const match = key.match(/faqs\[(\d+)\]\.(.+)/);
      if (match) {
        const index = parseInt(match[1]);
        const field = match[2];
        if (!faqs[index]) {
          faqs[index] = { displayOrder: index + 1 };
        }
        faqs[index][field] =
          field === "displayOrder"
            ? parseInt(stringValue) || index + 1
            : stringValue;
      }
    } else if (
      !key.includes("_arabic") &&
      !key.includes("_french") &&
      !key.includes("_english")
    ) {
      if (key === "brandId" && value === "") {
        cleanedData.append(key, "null");
      } else {
        cleanedData.append(key, value);
      }
    }
  }

  categoryIds.forEach((id) => cleanedData.append("categoryIds", id));

  if (content.length > 0) {
    cleanedData.append("content", JSON.stringify(content));
  }

  // Add sections if any exist
  if (sections.length > 0) {
    const validSections = sections.filter(
      (section) => section && section.title && section.description
    );
    if (validSections.length > 0) {
      cleanedData.append("sections", JSON.stringify(validSections));
    }
  }

  // Add faqs if any exist
  if (faqs.length > 0) {
    const validFaqs = faqs.filter((faq) => faq && faq.question && faq.answer);
    if (validFaqs.length > 0) {
      cleanedData.append("faqs", JSON.stringify(validFaqs));
    }
  }

  return cleanedData;
}
