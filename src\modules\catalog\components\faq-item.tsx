"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import WarnInput from "@/components/input/warn-input";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import { Language } from "@/modules/seo/types/multilanguage-seo";

interface MultilanguageFaqContent {
  question: string;
  answer: string;
  language: string;
}

interface MultilanguageFaq {
  displayOrder: number;
  content: MultilanguageFaqContent[];
}

interface FaqItemProps {
  faq: MultilanguageFaq;
  index: number;
  onUpdate: (faq: MultilanguageFaq) => void;
  onRemove: () => void;
  activeLanguage: Language;
}

export default function FaqItem({
  faq,
  index,
  onUpdate,
  onRemove,
  activeLanguage,
}: FaqItemProps) {
  const t = useTranslations("shared.forms.upload");

  const getCurrentLanguageContent = () => {
    const languageMap = {
      arabic: "Arabic",
      french: "French",
      english: "English",
    };

    return (
      faq.content.find(
        (content) => content.language === languageMap[activeLanguage]
      ) || { question: "", answer: "", language: languageMap[activeLanguage] }
    );
  };

  const handleContentChange = (field: "question" | "answer", value: string) => {
    const languageMap = {
      arabic: "Arabic",
      french: "French",
      english: "English",
    };

    const updatedContent = [...faq.content];
    const currentLangContent = languageMap[activeLanguage];
    const existingIndex = updatedContent.findIndex(
      (content) => content.language === currentLangContent
    );

    if (existingIndex >= 0) {
      updatedContent[existingIndex] = {
        ...updatedContent[existingIndex],
        [field]: value,
      };
    } else {
      updatedContent.push({
        question: field === "question" ? value : "",
        answer: field === "answer" ? value : "",
        language: currentLangContent,
      });
    }

    onUpdate({
      ...faq,
      content: updatedContent,
    });
  };

  const handleDisplayOrderChange = (value: number) => {
    onUpdate({
      ...faq,
      displayOrder: value,
    });
  };

  const currentContent = getCurrentLanguageContent();

  const getFieldName = (fieldName: string) => {
    return `faqs[${index}].${fieldName}_${activeLanguage}`;
  };

  return (
    <Card className="p-4 space-y-4">
      <div className="flex justify-between items-center">
        <Text textStyle="TS6" className="font-medium">
          {t("productLabels.faq")} {index + 1}
        </Text>
        <Button
          type="button"
          variant="outline"
          size="icon"
          onClick={onRemove}
          className="text-red-500 hover:text-red-700"
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>

      <LanguageTabs
        options={[
          { key: "arabic", value: t("languages.arabic") },
          { key: "french", value: t("languages.french") },
          { key: "english", value: t("languages.english") },
        ]}
        onSelect={(language) => onLanguageChange(language as Language)}
        selectedValue={activeLanguage}
      />

      {/* Hidden inputs for all languages to ensure form submission includes all data */}
      {["arabic", "french", "english"].map((lang) => {
        const langKey = lang.charAt(0).toUpperCase() + lang.slice(1);
        const langContent = faq.content.find(
          (content) => content.language === langKey
        ) || { question: "", answer: "", language: langKey };

        return (
          <div key={lang} style={{ display: "none" }}>
            <input
              type="hidden"
              name={`faqs[${index}].question_${lang}`}
              value={langContent.question || ""}
              readOnly
            />
            <input
              type="hidden"
              name={`faqs[${index}].answer_${lang}`}
              value={langContent.answer || ""}
              readOnly
            />
          </div>
        );
      })}

      <div className="grid grid-cols-1 gap-4">
        <div>
          <Label htmlFor={`faq-question-${index}-${activeLanguage}`}>
            {t("productLabels.faqQuestion")} {activeLanguage} {t("required")}
          </Label>
          <Input
            id={`faq-question-${index}-${activeLanguage}`}
            name={getFieldName("question")}
            value={currentContent.question || ""}
            onChange={(e) => handleContentChange("question", e.target.value)}
            placeholder={t("productLabels.faqQuestion")}
          />
        </div>

        <div>
          <Label htmlFor={`faq-answer-${index}-${activeLanguage}`}>
            {t("productLabels.faqAnswer")} {activeLanguage} {t("required")}
          </Label>
          <Textarea
            id={`faq-answer-${index}-${activeLanguage}`}
            name={getFieldName("answer")}
            value={currentContent.answer || ""}
            onChange={(e) => handleContentChange("answer", e.target.value)}
            placeholder={t("productLabels.faqAnswer")}
            rows={3}
          />
        </div>

        <div>
          <Label htmlFor={`faq-displayOrder-${index}`}>
            {t("productLabels.displayOrder")} {t("optional")}
          </Label>
          <WarnInput
            id={`faq-displayOrder-${index}`}
            name={`faqs[${index}].displayOrder`}
            type="number"
            value={faq.displayOrder || ""}
            onChange={(e) =>
              handleDisplayOrderChange(parseInt(e.target.value) || 0)
            }
            onWheel={disableScrollOnNumberInput}
            placeholder={t("productLabels.displayOrder")}
          />
        </div>
      </div>
    </Card>
  );
}
