export default function cleanProductCreationFormData(
  formData: FormData
): FormData {
  const filteredFormData = new FormData();
  const categoryIds: string[] = [];
  const sections: any[] = [];
  const faqs: any[] = [];
  const content: Array<{
    name: string;
    description: string;
    details: string;
    language: string;
  }> = [];
  const languages = ["arabic", "french", "english"];
  const sectionContentMap: { [key: number]: { [key: string]: any } } = {};
  const faqContentMap: { [key: number]: { [key: string]: any } } = {};

  const metaFieldsToExclude = [
    "metaTitle_arabic",
    "metaDescription_arabic",
    "metaTitle_french",
    "metaDescription_french",
    "metaTitle_english",
    "metaDescription_english",
    "keywords",
    "seoContent",
  ];

  formData.forEach((value, key) => {
    const stringValue = value as string;

    if (key === "displayOrder") {
      if (stringValue.trim() !== "") filteredFormData.append(key, value);
    } else if (key === "categoryIds") {
      categoryIds.push(stringValue);
    } else if (key.startsWith("sections[")) {
      const sectionMatch = key.match(/sections\[(\d+)\]\.(.+)_(.+)/);
      const displayOrderMatch = key.match(/sections\[(\d+)\]\.displayOrder$/);

      if (sectionMatch) {
        const index = parseInt(sectionMatch[1]);
        const field = sectionMatch[2];
        const language = sectionMatch[3];

        if (!sectionContentMap[index]) {
          sectionContentMap[index] = {};
        }
        if (!sectionContentMap[index][language]) {
          sectionContentMap[index][language] = {
            language: language.charAt(0).toUpperCase() + language.slice(1),
          };
        }
        sectionContentMap[index][language][field] = stringValue;
      } else if (displayOrderMatch) {
        const index = parseInt(displayOrderMatch[1]);
        if (!sections[index]) {
          sections[index] = { displayOrder: index + 1, content: [] };
        }
        sections[index].displayOrder = parseInt(stringValue) || index + 1;
      }
    } else if (key.startsWith("faqs[")) {
      const faqMatch = key.match(/faqs\[(\d+)\]\.(.+)_(.+)/);
      const displayOrderMatch = key.match(/faqs\[(\d+)\]\.displayOrder$/);

      if (faqMatch) {
        const index = parseInt(faqMatch[1]);
        const field = faqMatch[2];
        const language = faqMatch[3];

        if (!faqContentMap[index]) {
          faqContentMap[index] = {};
        }
        if (!faqContentMap[index][language]) {
          faqContentMap[index][language] = {
            language: language.charAt(0).toUpperCase() + language.slice(1),
          };
        }
        faqContentMap[index][language][field] = stringValue;
      } else if (displayOrderMatch) {
        const index = parseInt(displayOrderMatch[1]);
        if (!faqs[index]) {
          faqs[index] = { displayOrder: index + 1, content: [] };
        }
        faqs[index].displayOrder = parseInt(stringValue) || index + 1;
      }
    } else if (
      !key.includes("_arabic") &&
      !key.includes("_french") &&
      !key.includes("_english") &&
      typeof value === "string" &&
      value.trim() !== ""
    ) {
      filteredFormData.append(key, value);
    }
  });

  languages.forEach((lang) => {
    const nameKey = `name_${lang}`;
    const descKey = `description_${lang}`;
    const detailsKey = `details_${lang}`;

    let nameValue = "";
    let descValue = "";
    let detailsValue = "";

    if (formData.has(nameKey)) {
      nameValue = (formData.get(nameKey) as string)?.trim() || "";
    }

    if (formData.has(descKey)) {
      descValue = (formData.get(descKey) as string)?.trim() || "";
    }

    if (formData.has(detailsKey)) {
      detailsValue = (formData.get(detailsKey) as string)?.trim() || "";
    }

    if (nameValue !== "" || descValue !== "" || detailsValue !== "") {
      content.push({
        name: nameValue,
        description: descValue,
        details: detailsValue,
        language: lang.charAt(0).toUpperCase() + lang.slice(1),
      });
    }
  });

  if (content.length > 0) {
    filteredFormData.append("content", JSON.stringify(content));
  }

  categoryIds.forEach((id) => filteredFormData.append("categoryIds", id));

  // Process sections content
  Object.keys(sectionContentMap).forEach((indexStr) => {
    const index = parseInt(indexStr);
    if (!sections[index]) {
      sections[index] = { displayOrder: index + 1, content: [] };
    }

    Object.keys(sectionContentMap[index]).forEach((lang) => {
      const langContent = sectionContentMap[index][lang];
      if (langContent.title && langContent.description) {
        sections[index].content.push(langContent);
      }
    });
  });

  // Process faqs content
  Object.keys(faqContentMap).forEach((indexStr) => {
    const index = parseInt(indexStr);
    if (!faqs[index]) {
      faqs[index] = { displayOrder: index + 1, content: [] };
    }

    Object.keys(faqContentMap[index]).forEach((lang) => {
      const langContent = faqContentMap[index][lang];
      if (langContent.question && langContent.answer) {
        faqs[index].content.push(langContent);
      }
    });
  });

  // Add sections if any exist
  if (sections.length > 0) {
    const validSections = sections.filter(
      (section) => section && section.content && section.content.length > 0
    );
    if (validSections.length > 0) {
      filteredFormData.append("sections", JSON.stringify(validSections));
    }
  }

  // Add faqs if any exist
  if (faqs.length > 0) {
    const validFaqs = faqs.filter(
      (faq) => faq && faq.content && faq.content.length > 0
    );
    if (validFaqs.length > 0) {
      filteredFormData.append("faqs", JSON.stringify(validFaqs));
    }
  }

  return filteredFormData;
}
